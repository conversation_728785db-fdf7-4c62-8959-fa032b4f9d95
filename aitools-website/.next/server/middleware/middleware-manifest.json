{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_915e9f02._.js", "server/edge/chunks/node_modules_@formatjs_intl-localematcher_lib_f4580406._.js", "server/edge/chunks/node_modules_4ac6bddb._.js", "server/edge/chunks/[root-of-the-server]__e8693e40._.js", "server/edge/chunks/edge-wrapper_ff61254e.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "z32E33zlP9nvGtJvjxN4efsCMH50n6HIiGeqjezkGUw=", "__NEXT_PREVIEW_MODE_ID": "d648355df344db6870cf6c98d9a50dd6", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "5354e733c290fec234d81b2a375cc7699b36f36937876e9c394a04ee91ce4d23", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d758c20224a5026fae644fc98f73b9a642ef6d97ffca3cff2afd7abf322e6bad"}}}, "instrumentation": null, "functions": {}}